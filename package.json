{"$schema": "https://json.schemastore.org/package.json", "name": "backend-hrms", "version": "1.0.0", "description": "Backend API for Human Resource Management System", "main": "index.js", "scripts": {"start": "node index.js", "dev": "nodemon index.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["hrms", "backend", "api", "express", "nodejs"], "author": "", "license": "ISC", "dependencies": {"express": "^4.18.2"}, "devDependencies": {"nodemon": "^3.1.10"}}